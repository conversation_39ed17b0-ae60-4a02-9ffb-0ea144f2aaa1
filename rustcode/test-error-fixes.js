// Test script to verify Monaco Editor error fixes
// Run this in the browser console to test error handling

function testMonacoErrorFixes() {
    console.log('🧪 Testing Monaco Editor error fixes...');
    
    // Test 1: Check if Monaco Error Handler is available
    console.log('\n1. Testing Monaco Error Handler availability...');
    if (window.monacoErrorHandler) {
        console.log('✅ Monaco Error Handler is available');
        console.log('📊 Error stats:', window.monacoErrorHandler.getErrorStats());
    } else {
        console.log('❌ Monaco Error Handler not found');
    }
    
    // Test 2: Check if Monaco Editor is properly initialized
    console.log('\n2. Testing Monaco Editor initialization...');
    if (typeof monaco !== 'undefined' && monaco.editor) {
        console.log('✅ Monaco Editor is available');
        
        // Test creating a model safely
        try {
            const testModel = monaco.editor.createModel('console.log("test");', 'javascript');
            console.log('✅ Model creation successful');
            testModel.dispose();
        } catch (error) {
            console.log('❌ Model creation failed:', error.message);
        }
    } else {
        console.log('❌ Monaco Editor not available');
    }
    
    // Test 3: Test file opening functionality
    console.log('\n3. Testing file opening functionality...');
    if (window.rustCodeApp && window.rustCodeApp.editorManager) {
        try {
            window.rustCodeApp.editorManager.openFile('// Test file content\nconsole.log("Hello World");', 'javascript');
            console.log('✅ File opening successful');
        } catch (error) {
            console.log('❌ File opening failed:', error.message);
        }
    } else {
        console.log('❌ RustCode app or editor manager not available');
    }
    
    // Test 4: Test error console functionality
    console.log('\n4. Testing error console functionality...');
    if (window.errorConsole) {
        console.log('✅ Error console is available');
        
        // Test logging different types of errors
        window.errorConsole.logInfo('Test info message from error fix test', 'test-error-fixes.js', 1);
        window.errorConsole.logWarning('Test warning message from error fix test', 'test-error-fixes.js', 2);
        window.errorConsole.logError('Test error message from error fix test', 'test-error-fixes.js', 3);
        
        console.log('✅ Error console test messages logged');
    } else {
        console.log('❌ Error console not available');
    }
    
    // Test 5: Test Monaco worker configuration
    console.log('\n5. Testing Monaco worker configuration...');
    if (window.MonacoEnvironment && window.MonacoEnvironment.getWorker) {
        console.log('✅ Monaco worker environment is configured');
        
        // Test worker creation
        try {
            const testWorker = window.MonacoEnvironment.getWorker('test', 'javascript');
            if (testWorker && typeof testWorker.postMessage === 'function') {
                console.log('✅ Worker creation successful');
                
                // Test worker communication
                testWorker.postMessage({ id: 'test', method: 'initialize' });
                console.log('✅ Worker communication test sent');
            } else {
                console.log('❌ Worker creation returned invalid worker');
            }
        } catch (error) {
            console.log('❌ Worker creation failed:', error.message);
        }
    } else {
        console.log('❌ Monaco worker environment not configured');
    }
    
    // Test 6: Test language service configuration
    console.log('\n6. Testing language service configuration...');
    if (monaco && monaco.languages && monaco.languages.typescript) {
        try {
            const jsOptions = monaco.languages.typescript.javascriptDefaults.getDiagnosticsOptions();
            const tsOptions = monaco.languages.typescript.typescriptDefaults.getDiagnosticsOptions();
            
            console.log('✅ Language services are configured');
            console.log('📋 JavaScript diagnostics:', jsOptions);
            console.log('📋 TypeScript diagnostics:', tsOptions);
        } catch (error) {
            console.log('❌ Language service configuration failed:', error.message);
        }
    } else {
        console.log('❌ Language services not available');
    }
    
    // Test 7: Simulate common errors to test error handling
    console.log('\n7. Testing error handling with simulated errors...');
    
    // Reset error count for clean test
    if (window.monacoErrorHandler) {
        window.monacoErrorHandler.resetErrorCount();
    }
    
    // Simulate factory.create error
    console.log('🔧 Simulating factory.create error...');
    console.error('Test error: factory.create is not a function');
    
    // Simulate worker error
    console.log('🔧 Simulating worker error...');
    console.error('Test error: Monaco worker failed to initialize');
    
    // Check if errors were handled
    setTimeout(() => {
        if (window.monacoErrorHandler) {
            const stats = window.monacoErrorHandler.getErrorStats();
            console.log('📊 Error handling stats after simulation:', stats);
            
            if (stats.errorCount > 0) {
                console.log('✅ Error handling is working - errors were captured');
            } else {
                console.log('⚠️ Error handling may not be working - no errors captured');
            }
        }
    }, 100);
    
    console.log('\n🎉 Monaco Editor error fix testing completed!');
    console.log('📝 Check the error console (Ctrl+Shift+E) to see captured errors');
}

// Auto-run the test if this script is loaded
if (typeof window !== 'undefined') {
    // Wait for the app to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testMonacoErrorFixes, 1000);
        });
    } else {
        setTimeout(testMonacoErrorFixes, 1000);
    }
}

// Make the test function globally available
if (typeof window !== 'undefined') {
    window.testMonacoErrorFixes = testMonacoErrorFixes;
}

console.log('🔧 Monaco Editor error fix test script loaded. Run testMonacoErrorFixes() to test.');
