// Monaco Editor E<PERSON><PERSON>
// Provides robust error handling for Monaco Editor operations

export class MonacoErrorHandler {
    constructor() {
        this.isMonacoAvailable = false;
        this.errorCount = 0;
        this.maxErrors = 10;
        this.init();
    }

    init() {
        try {
            // Check if Monaco is available
            this.isMonacoAvailable = typeof monaco !== 'undefined' && monaco.editor;
            
            if (this.isMonacoAvailable) {
                this.setupErrorInterception();
                console.log('Monaco Error Handler initialized');
            } else {
                console.warn('Monaco Editor not available, error handler disabled');
            }
        } catch (error) {
            console.error('Failed to initialize Monaco Error Handler:', error);
        }
    }

    setupErrorInterception() {
        // Intercept Monaco Editor errors
        const originalConsoleError = console.error;
        console.error = (...args) => {
            // Check if this is a Monaco-related error
            const errorMessage = args.join(' ');
            if (this.isMonacoError(errorMessage)) {
                this.handleMonacoError(errorMessage, args);
            }
            // Call original console.error
            originalConsoleError.apply(console, args);
        };

        // Intercept unhandled promise rejections that might be Monaco-related
        window.addEventListener('unhandledrejection', (event) => {
            if (this.isMonacoError(event.reason?.message || event.reason)) {
                this.handleMonacoError('Promise rejection: ' + (event.reason?.message || event.reason));
                event.preventDefault(); // Prevent the error from being logged again
            }
        });
    }

    isMonacoError(errorMessage) {
        if (typeof errorMessage !== 'string') {
            errorMessage = String(errorMessage);
        }

        const monacoErrorPatterns = [
            'factory.create is not a function',
            'monaco',
            'editor.create',
            'createModel',
            'setModel',
            'language service',
            'worker',
            'vs/editor',
            'vs/language'
        ];

        return monacoErrorPatterns.some(pattern => 
            errorMessage.toLowerCase().includes(pattern.toLowerCase())
        );
    }

    handleMonacoError(errorMessage, originalArgs = []) {
        this.errorCount++;
        
        // Log to error console if available
        if (window.errorConsole) {
            window.errorConsole.logError(
                `Monaco Editor Error: ${errorMessage}`,
                'monaco-editor',
                0
            );
        }

        // If too many errors, disable Monaco features
        if (this.errorCount >= this.maxErrors) {
            console.warn('Too many Monaco errors, disabling advanced features');
            this.disableAdvancedFeatures();
        }

        // Try to recover from common errors
        this.attemptRecovery(errorMessage);
    }

    attemptRecovery(errorMessage) {
        try {
            if (errorMessage.includes('factory.create is not a function')) {
                // This usually means a language service failed to initialize
                console.log('Attempting to recover from factory.create error...');
                this.resetLanguageServices();
            }

            if (errorMessage.includes('createModel')) {
                // Model creation failed, try with plaintext
                console.log('Attempting to recover from createModel error...');
                this.fallbackToPlaintext();
            }

            if (errorMessage.includes('worker')) {
                // Worker failed, disable workers
                console.log('Attempting to recover from worker error...');
                this.disableWorkers();
            }
        } catch (recoveryError) {
            console.warn('Recovery attempt failed:', recoveryError);
        }
    }

    resetLanguageServices() {
        try {
            if (this.isMonacoAvailable && monaco.languages) {
                // Reset TypeScript/JavaScript language services
                if (monaco.languages.typescript) {
                    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
                        noSemanticValidation: true,
                        noSyntaxValidation: true
                    });
                    
                    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
                        noSemanticValidation: true,
                        noSyntaxValidation: true
                    });
                }
            }
        } catch (error) {
            console.warn('Failed to reset language services:', error);
        }
    }

    fallbackToPlaintext() {
        // This method can be called by the editor to force plaintext mode
        if (window.rustCodeApp && window.rustCodeApp.editorManager) {
            try {
                const editor = window.rustCodeApp.editorManager;
                if (editor.currentModel) {
                    monaco.editor.setModelLanguage(editor.currentModel, 'plaintext');
                }
            } catch (error) {
                console.warn('Failed to fallback to plaintext:', error);
            }
        }
    }

    disableWorkers() {
        try {
            // Override the worker configuration to prevent worker creation
            if (window.MonacoEnvironment) {
                const originalGetWorker = window.MonacoEnvironment.getWorker;
                window.MonacoEnvironment.getWorker = function() {
                    // Return a more robust mock worker
                    return {
                        postMessage: () => {},
                        addEventListener: () => {},
                        removeEventListener: () => {},
                        terminate: () => {},
                        onmessage: null,
                        onerror: null
                    };
                };
            }
        } catch (error) {
            console.warn('Failed to disable workers:', error);
        }
    }

    disableAdvancedFeatures() {
        try {
            // Disable features that commonly cause errors
            if (this.isMonacoAvailable) {
                // Disable hover providers
                if (monaco.languages.registerHoverProvider) {
                    const originalRegisterHover = monaco.languages.registerHoverProvider;
                    monaco.languages.registerHoverProvider = () => ({ dispose: () => {} });
                }

                // Disable completion providers
                if (monaco.languages.registerCompletionItemProvider) {
                    const originalRegisterCompletion = monaco.languages.registerCompletionItemProvider;
                    monaco.languages.registerCompletionItemProvider = () => ({ dispose: () => {} });
                }
            }
        } catch (error) {
            console.warn('Failed to disable advanced features:', error);
        }
    }

    // Safe wrapper for Monaco operations
    safeMonacoOperation(operation, fallback = null) {
        try {
            if (!this.isMonacoAvailable) {
                console.warn('Monaco not available, skipping operation');
                return fallback;
            }
            return operation();
        } catch (error) {
            this.handleMonacoError(`Safe operation failed: ${error.message}`);
            return fallback;
        }
    }

    // Reset error count (useful for testing)
    resetErrorCount() {
        this.errorCount = 0;
    }

    // Get current error statistics
    getErrorStats() {
        return {
            errorCount: this.errorCount,
            maxErrors: this.maxErrors,
            isMonacoAvailable: this.isMonacoAvailable
        };
    }
}

// Create global instance
export const monacoErrorHandler = new MonacoErrorHandler();

// Make it globally available
if (typeof window !== 'undefined') {
    window.monacoErrorHandler = monacoErrorHandler;
}
