/* Git Integration Styles */
.git-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    font-family: var(--font-family);
    overflow: hidden;
}

.git-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    min-height: 48px;
}

.git-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.git-controls {
    display: flex;
    gap: 4px;
}

.git-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.git-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.git-branch-info {
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.current-branch {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.branch-icon {
    color: var(--accent-color);
}

.branch-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.branch-dropdown-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.branch-dropdown-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.git-status .status-text {
    font-size: 12px;
    color: var(--text-secondary);
}

.git-status .status-text.clean {
    color: var(--success-color);
}

.git-status .status-text.modified {
    color: var(--warning-color);
}

.git-status .status-text.staged {
    color: var(--accent-color);
}

.git-actions {
    display: flex;
    gap: 8px;
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.git-action-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
    justify-content: center;
}

.git-action-btn:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.git-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.git-action-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.git-action-btn.primary:hover:not(:disabled) {
    background: var(--accent-color-hover);
}

.git-sections {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.git-section {
    margin-bottom: 8px;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.section-header:hover {
    background: var(--bg-hover);
}

.section-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-title {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    flex: 1;
}

.section-count {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    min-width: 18px;
    text-align: center;
}

.section-actions {
    display: flex;
    gap: 4px;
}

.section-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-action-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.section-content {
    max-height: 300px;
    overflow-y: auto;
}

.section-content.collapsed {
    display: none;
}

.git-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
    cursor: pointer;
}

.git-file-item:hover {
    background: var(--bg-hover);
}

.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.file-status-icon {
    color: var(--text-secondary);
    flex-shrink: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-path {
    color: var(--text-secondary);
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.git-file-item:hover .file-actions {
    opacity: 1;
}

.file-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.git-tabs {
    display: flex;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.git-tab {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 12px 16px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    flex: 1;
}

.git-tab:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.git-tab.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.git-tab-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.tab-panel {
    display: none;
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
}

.commit-input-section {
    display: none;
    padding: 16px;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

#commit-message {
    width: 100%;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-family: var(--font-family);
    font-size: 13px;
    resize: vertical;
    min-height: 60px;
    margin-bottom: 12px;
}

#commit-message:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

#commit-message::placeholder {
    color: var(--text-secondary);
}

.commit-actions {
    display: flex;
    gap: 8px;
}

.commit-action-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex: 1;
}

.commit-action-btn:hover {
    background: var(--accent-color-hover);
}

.commit-action-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.commit-action-btn.secondary:hover {
    background: var(--bg-hover);
}

.branch-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.branch-dropdown.hidden {
    display: none;
}

.dropdown-header {
    padding: 8px;
    border-bottom: 1px solid var(--border-color);
}

#branch-search {
    width: 100%;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
}

#branch-search:focus {
    outline: none;
    border-color: var(--accent-color);
}

.branch-dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.branch-dropdown-item:hover {
    background: var(--bg-hover);
}

.branch-dropdown-item.current {
    background: var(--accent-color-alpha);
    color: var(--accent-color);
}

.diff-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-primary);
    z-index: 2000;
    display: flex;
    flex-direction: column;
}

.diff-viewer.hidden {
    display: none;
}

.diff-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.diff-file-name {
    font-weight: 500;
    color: var(--text-primary);
}

.diff-close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.diff-close-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.diff-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    font-family: var(--font-mono);
    font-size: 13px;
    line-height: 1.4;
}

.diff-line {
    padding: 2px 8px;
    white-space: pre;
    border-left: 3px solid transparent;
}

.diff-line.added {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
    color: #28a745;
}

.diff-line.removed {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
    color: #dc3545;
}

.diff-line.unchanged {
    color: var(--text-primary);
}

/* Branch, Remote, and Commit Lists */
.branch-actions,
.remote-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.branch-action-btn,
.remote-action-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.branch-action-btn:hover,
.remote-action-btn:hover {
    background: var(--accent-color-hover);
}

.branch-list-item,
.remote-list-item,
.commit-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.branch-list-item:hover,
.remote-list-item:hover,
.commit-item:hover {
    background: var(--bg-hover);
}

.branch-list-item.current {
    border-color: var(--accent-color);
    background: var(--accent-color-alpha);
}

.branch-info,
.remote-info,
.commit-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 0;
}

.branch-name,
.remote-name,
.commit-message {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
}

.current-indicator {
    background: var(--accent-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
}

.remote-url,
.commit-meta {
    color: var(--text-secondary);
    font-size: 11px;
}

.commit-meta {
    display: flex;
    gap: 12px;
}

.commit-id {
    font-family: var(--font-mono);
    background: var(--bg-tertiary);
    padding: 2px 4px;
    border-radius: 3px;
}

.empty-state {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 32px 16px;
}

/* Git Settings Modal */
.git-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.git-settings-modal.hidden {
    display: none;
}

.git-settings-modal .settings-content {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .git-actions {
        flex-direction: column;
    }
    
    .git-action-btn {
        flex: none;
    }
    
    .commit-actions {
        flex-direction: column;
    }
    
    .branch-actions,
    .remote-actions {
        flex-direction: column;
    }
}
