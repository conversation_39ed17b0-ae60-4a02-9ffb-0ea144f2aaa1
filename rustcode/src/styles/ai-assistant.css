/* AI Assistant Styles */
.ai-assistant-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    font-family: var(--font-family);
}

.ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    min-height: 48px;
}

.ai-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.ai-controls {
    display: flex;
    gap: 4px;
}

.ai-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.ai-mode-selector {
    display: flex;
    padding: 8px 16px;
    gap: 4px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.mode-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.mode-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.mode-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.ai-chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.ai-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.ai-message {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ai-message.user {
    align-items: flex-end;
}

.ai-message.assistant {
    align-items: flex-start;
}

.ai-message.error {
    align-items: flex-start;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.message-sender {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.message-time {
    opacity: 0.7;
}

.message-content {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 12px;
    line-height: 1.5;
    font-size: 14px;
    word-wrap: break-word;
}

.ai-message.user .message-content {
    background: var(--accent-color);
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-message.assistant .message-content {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
}

.ai-message.error .message-content {
    background: var(--error-bg);
    color: var(--error-color);
    border: 1px solid var(--error-border);
    border-bottom-left-radius: 4px;
}

.code-block {
    margin: 8px 0;
    border-radius: 6px;
    overflow: hidden;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    font-size: 12px;
}

.code-lang {
    color: var(--text-secondary);
    font-weight: 500;
}

.copy-code-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.copy-code-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.code-block pre {
    margin: 0;
    padding: 12px;
    overflow-x: auto;
    font-family: var(--font-mono);
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-primary);
}

.code-block code {
    background: none;
    padding: 0;
    font-family: inherit;
}

.inline-code {
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: var(--font-mono);
    font-size: 0.9em;
    color: var(--text-primary);
}

.ai-input-container {
    padding: 16px;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.ai-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    overflow: hidden;
}

.ai-input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

#ai-input {
    border: none;
    background: none;
    padding: 12px;
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 14px;
    resize: none;
    outline: none;
    min-height: 60px;
    max-height: 120px;
}

#ai-input::placeholder {
    color: var(--text-secondary);
}

.ai-input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.ai-send-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.ai-send-btn:hover:not(:disabled) {
    background: var(--accent-color-hover);
}

.ai-send-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.ai-attach-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.ai-attach-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.ai-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--text-secondary);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.status-indicator.processing {
    background: var(--warning-color);
    animation: pulse 1.5s infinite;
}

.status-indicator.error {
    background: var(--error-color);
}

.status-indicator.success {
    background: var(--success-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Settings Modal */
.ai-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.ai-settings-modal.hidden {
    display: none;
}

.settings-content {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.settings-content h3 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 6px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.setting-group input[type="text"],
.setting-group input[type="password"],
.setting-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
    font-family: var(--font-family);
}

.setting-group input[type="text"]:focus,
.setting-group input[type="password"]:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

.setting-group small {
    display: block;
    margin-top: 4px;
    color: var(--text-secondary);
    font-size: 12px;
}

.setting-group small a {
    color: var(--accent-color);
    text-decoration: none;
}

.setting-group small a:hover {
    text-decoration: underline;
}

.settings-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.btn-primary {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--accent-color-hover);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ai-mode-selector {
        padding: 8px 12px;
    }
    
    .mode-btn {
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .ai-messages {
        padding: 12px;
    }
    
    .message-content {
        max-width: 90%;
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .ai-input-container {
        padding: 12px;
    }
    
    .settings-content {
        padding: 20px;
        margin: 20px;
    }
}
