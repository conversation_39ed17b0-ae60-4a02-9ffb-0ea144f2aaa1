/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables */
:root {
    --bg-primary: #1e1e1e;
    --bg-secondary: #252526;
    --bg-tertiary: #2d2d30;
    --bg-hover: #3e3e42;
    --border-color: #3e3e42;
    --text-primary: #cccccc;
    --text-secondary: #969696;
    --accent-color: #007acc;
    --accent-color-hover: #005a9e;
    --accent-color-alpha: rgba(0, 122, 204, 0.2);
    --error-color: #f14c4c;
    --error-bg: rgba(241, 76, 76, 0.1);
    --error-border: rgba(241, 76, 76, 0.3);
    --warning-color: #ffcc02;
    --success-color: #89d185;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
}

body {
    font-family: var(--font-family);
    font-size: 13px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Title Bar */
#title-bar {
    background-color: #2d2d30;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    border-bottom: 1px solid #3e3e42;
    user-select: none;
}

.title-bar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.title-bar-title {
    font-size: 12px;
    color: #cccccc;
}

.title-bar-controls {
    display: flex;
    gap: 5px;
}

.title-bar-button {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 14px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 2px;
}

.title-bar-button:hover {
    background-color: #3e3e42;
}

.title-bar-button:last-child:hover {
    background-color: #e74c3c;
}

/* Menu Bar */
#menu-bar {
    background-color: #2d2d30;
    height: 30px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid #3e3e42;
}

.menu-item {
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 3px;
    font-size: 12px;
    display: flex;
    align-items: center;
    color: #cccccc;
}

.menu-item:hover {
    background-color: #3e3e42;
}

/* Main Content */
#main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.panel {
    background-color: #252526;
    border-right: 1px solid #3e3e42;
}

/* Sidebar */
#sidebar {
    width: 250px;
    min-width: 200px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
}

.sidebar-title {
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    color: #cccccc;
}

.sidebar-actions {
    display: flex;
    gap: 5px;
}

.sidebar-actions button {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
}

.sidebar-actions button:hover {
    background-color: #3e3e42;
}

.sidebar-actions button svg {
    color: #cccccc;
}

/* Resize Handle */
#resize-handle {
    width: 4px;
    background-color: #3e3e42;
    cursor: col-resize;
    position: relative;
}

#resize-handle:hover {
    background-color: #007acc;
}

/* Editor Area */
#editor-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-right: none;
    position: relative;
}

#editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 200px;
}

/* Welcome Screen */
#welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #1e1e1e;
}

.welcome-content {
    text-align: center;
    max-width: 400px;
}

.welcome-content h1 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #ffffff;
}

.welcome-content p {
    font-size: 14px;
    margin-bottom: 20px;
    color: #cccccc;
}

.welcome-features {
    margin-bottom: 30px;
}

.feature {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #cccccc;
    font-size: 13px;
}

.feature-icon {
    margin-right: 10px;
    font-size: 16px;
}

.welcome-shortcuts {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #3e3e42;
}

.welcome-shortcuts p {
    font-size: 12px;
    color: #888;
    margin: 0;
}

.welcome-shortcuts kbd {
    background-color: #3e3e42;
    color: #cccccc;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
}

.welcome-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.welcome-button {
    background-color: #007acc;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
}

.welcome-button:hover {
    background-color: #005a9e;
}

/* AI Assistant Panel */
#ai-assistant-panel {
    min-width: 350px;
    max-width: 600px;
    border-left: 1px solid var(--border-color);
    border-right: none;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
    background: #3e3e42;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4e4e52;
}

/* Selection */
::selection {
    background-color: #264f78;
}

/* Focus styles */
button:focus,
input:focus {
    outline: 1px solid #007acc;
    outline-offset: -1px;
}
