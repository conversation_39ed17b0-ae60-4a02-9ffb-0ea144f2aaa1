import { createIcon } from '../icons/icon-library.js';

export class AIAssistant {
    constructor(container, editorManager) {
        this.container = container;
        this.editorManager = editorManager;
        this.isVisible = false;
        this.apiKey = localStorage.getItem('gemini-api-key') || '';
        this.conversationHistory = [];
        this.isProcessing = false;
        this.agenticMode = false;
        this.currentTask = null;
        this.taskQueue = [];
        
        this.init();
    }

    init() {
        this.createUI();
        this.setupEventListeners();
        this.loadSettings();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="ai-assistant-panel">
                <div class="ai-header">
                    <div class="ai-title">
                        ${createIcon('ai', 16)} AI Assistant
                    </div>
                    <div class="ai-controls">
                        <button class="ai-btn" id="ai-settings-btn" title="Settings">
                            ${createIcon('settings', 14)}
                        </button>
                        <button class="ai-btn" id="ai-clear-btn" title="Clear Chat">
                            ${createIcon('clear', 14)}
                        </button>
                        <button class="ai-btn" id="ai-close-btn" title="Close">
                            ${createIcon('close', 14)}
                        </button>
                    </div>
                </div>

                <div class="ai-mode-selector">
                    <button class="mode-btn active" data-mode="chat">Chat</button>
                    <button class="mode-btn" data-mode="agentic">Agentic</button>
                    <button class="mode-btn" data-mode="code-gen">Code Gen</button>
                    <button class="mode-btn" data-mode="refactor">Refactor</button>
                    <button class="mode-btn" data-mode="debug">Debug</button>
                </div>

                <div class="ai-chat-container">
                    <div class="ai-messages" id="ai-messages"></div>
                    <div class="ai-input-container">
                        <div class="ai-input-wrapper">
                            <textarea 
                                id="ai-input" 
                                placeholder="Ask AI anything about your code..."
                                rows="3"
                            ></textarea>
                            <div class="ai-input-actions">
                                <button class="ai-send-btn" id="ai-send-btn" disabled>
                                    ${createIcon('send', 16)}
                                </button>
                                <button class="ai-attach-btn" id="ai-attach-btn" title="Attach current file">
                                    ${createIcon('attach', 16)}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ai-status" id="ai-status">
                    <span class="status-text">Ready</span>
                    <div class="status-indicator"></div>
                </div>

                <div class="ai-settings-modal hidden" id="ai-settings-modal">
                    <div class="settings-content">
                        <h3>AI Assistant Settings</h3>
                        <div class="setting-group">
                            <label for="gemini-api-key">Gemini API Key:</label>
                            <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key">
                            <small>Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>
                        </div>
                        <div class="setting-group">
                            <label for="ai-model">Model:</label>
                            <select id="ai-model">
                                <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash (Experimental)</option>
                                <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="agentic-auto-execute"> 
                                Auto-execute agentic suggestions
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="context-aware"> 
                                Include file context in requests
                            </label>
                        </div>
                        <div class="settings-actions">
                            <button class="btn-primary" id="save-ai-settings">Save</button>
                            <button class="btn-secondary" id="cancel-ai-settings">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Mode selector
        this.container.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setMode(e.target.dataset.mode);
            });
        });

        // Input handling
        const input = this.container.querySelector('#ai-input');
        const sendBtn = this.container.querySelector('#ai-send-btn');
        
        input.addEventListener('input', () => {
            sendBtn.disabled = !input.value.trim();
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => this.sendMessage());

        // Control buttons
        this.container.querySelector('#ai-settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        this.container.querySelector('#ai-clear-btn').addEventListener('click', () => {
            this.clearChat();
        });

        this.container.querySelector('#ai-close-btn').addEventListener('click', () => {
            this.hide();
        });

        this.container.querySelector('#ai-attach-btn').addEventListener('click', () => {
            this.attachCurrentFile();
        });

        // Settings modal
        this.container.querySelector('#save-ai-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        this.container.querySelector('#cancel-ai-settings').addEventListener('click', () => {
            this.hideSettings();
        });
    }

    setMode(mode) {
        this.container.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        this.container.querySelector(`[data-mode="${mode}"]`).classList.add('active');
        
        this.currentMode = mode;
        this.agenticMode = mode === 'agentic';
        
        // Update placeholder based on mode
        const input = this.container.querySelector('#ai-input');
        const placeholders = {
            chat: 'Ask AI anything about your code...',
            agentic: 'Describe what you want me to build or fix...',
            'code-gen': 'Describe the code you want to generate...',
            refactor: 'Describe how you want to refactor this code...',
            debug: 'Describe the bug or issue you\'re facing...'
        };
        input.placeholder = placeholders[mode] || placeholders.chat;
    }

    async sendMessage() {
        const input = this.container.querySelector('#ai-input');
        const message = input.value.trim();
        
        if (!message || this.isProcessing) return;
        
        if (!this.apiKey) {
            this.showError('Please set your Gemini API key in settings first.');
            this.showSettings();
            return;
        }

        this.isProcessing = true;
        this.updateStatus('Processing...', 'processing');
        
        // Add user message to chat
        this.addMessage('user', message);
        input.value = '';
        input.disabled = true;

        try {
            const response = await this.callGeminiAPI(message);
            this.addMessage('assistant', response);
            
            // Handle agentic mode
            if (this.agenticMode) {
                await this.handleAgenticResponse(response);
            }
            
        } catch (error) {
            console.error('AI request failed:', error);
            this.addMessage('error', `Error: ${error.message}`);
            this.showError('Failed to get AI response. Please check your API key and try again.');
        } finally {
            this.isProcessing = false;
            this.updateStatus('Ready', 'ready');
            input.disabled = false;
            input.focus();
        }
    }

    async callGeminiAPI(message) {
        const model = localStorage.getItem('ai-model') || 'gemini-2.0-flash-exp';
        const contextAware = localStorage.getItem('context-aware') === 'true';
        
        let prompt = message;
        
        // Add context if enabled
        if (contextAware && this.editorManager.editor) {
            const currentFile = this.getCurrentFileContext();
            if (currentFile) {
                prompt = `Context: I'm working on a file "${currentFile.name}" with the following content:\n\`\`\`${currentFile.language}\n${currentFile.content}\n\`\`\`\n\nUser request: ${message}`;
            }
        }

        // Add mode-specific system prompts
        const systemPrompts = {
            agentic: 'You are an autonomous coding assistant. Provide step-by-step implementation plans and code solutions. Be specific about file changes needed.',
            'code-gen': 'You are a code generation specialist. Generate clean, well-documented code based on requirements.',
            refactor: 'You are a code refactoring expert. Suggest improvements while maintaining functionality.',
            debug: 'You are a debugging expert. Analyze code issues and provide solutions.'
        };

        if (systemPrompts[this.currentMode]) {
            prompt = `${systemPrompts[this.currentMode]}\n\n${prompt}`;
        }

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                }
            })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('Invalid response from Gemini API');
        }

        return data.candidates[0].content.parts[0].text;
    }

    async handleAgenticResponse(response) {
        // Parse agentic commands from response
        const codeBlocks = this.extractCodeBlocks(response);
        const fileOperations = this.extractFileOperations(response);
        
        if (codeBlocks.length > 0 || fileOperations.length > 0) {
            const autoExecute = localStorage.getItem('agentic-auto-execute') === 'true';
            
            if (autoExecute) {
                await this.executeAgenticActions(codeBlocks, fileOperations);
            } else {
                this.showAgenticActions(codeBlocks, fileOperations);
            }
        }
    }

    extractCodeBlocks(text) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const blocks = [];
        let match;
        
        while ((match = codeBlockRegex.exec(text)) !== null) {
            blocks.push({
                language: match[1] || 'text',
                code: match[2].trim()
            });
        }
        
        return blocks;
    }

    extractFileOperations(text) {
        // Extract file operations like "create file", "modify file", etc.
        const operations = [];
        const lines = text.split('\n');
        
        for (const line of lines) {
            if (line.toLowerCase().includes('create file') || line.toLowerCase().includes('new file')) {
                const fileMatch = line.match(/["`']([^"`']+\.[a-zA-Z]+)["`']/);
                if (fileMatch) {
                    operations.push({
                        type: 'create',
                        file: fileMatch[1]
                    });
                }
            }
        }
        
        return operations;
    }

    getCurrentFileContext() {
        if (!this.editorManager.editor) return null;
        
        const model = this.editorManager.editor.getModel();
        if (!model) return null;
        
        // Get current tab info from the app
        const app = window.rustCodeApp;
        if (!app || !app.tabManager) return null;
        
        const activeTab = app.tabManager.getActiveTab();
        if (!activeTab) return null;
        
        return {
            name: activeTab.file_name,
            content: model.getValue(),
            language: model.getLanguageId()
        };
    }

    addMessage(type, content) {
        const messagesContainer = this.container.querySelector('#ai-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        
        if (type === 'user') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">You</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-content">${this.escapeHtml(content)}</div>
            `;
        } else if (type === 'assistant') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">${createIcon('ai', 14)} AI Assistant</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-content">${this.formatAIResponse(content)}</div>
            `;
        } else if (type === 'error') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">${createIcon('error', 14)} Error</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-content">${this.escapeHtml(content)}</div>
            `;
        }
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        this.conversationHistory.push({ type, content, timestamp });
    }

    formatAIResponse(content) {
        // Convert markdown-like formatting to HTML
        let formatted = this.escapeHtml(content);
        
        // Code blocks
        formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            return `<div class="code-block">
                <div class="code-header">
                    <span class="code-lang">${lang || 'text'}</span>
                    <button class="copy-code-btn" onclick="navigator.clipboard.writeText(\`${code.replace(/`/g, '\\`')}\`)">
                        ${createIcon('copy', 12)}
                    </button>
                </div>
                <pre><code>${code}</code></pre>
            </div>`;
        });
        
        // Inline code
        formatted = formatted.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
        
        // Bold text
        formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        
        // Italic text
        formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');
        
        // Line breaks
        formatted = formatted.replace(/\n/g, '<br>');
        
        return formatted;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    show() {
        this.isVisible = true;
        this.container.style.display = 'flex';
        this.container.querySelector('#ai-input').focus();
    }

    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    showSettings() {
        const modal = this.container.querySelector('#ai-settings-modal');
        modal.classList.remove('hidden');
        
        // Load current settings
        this.container.querySelector('#gemini-api-key').value = this.apiKey;
        this.container.querySelector('#ai-model').value = localStorage.getItem('ai-model') || 'gemini-2.0-flash-exp';
        this.container.querySelector('#agentic-auto-execute').checked = localStorage.getItem('agentic-auto-execute') === 'true';
        this.container.querySelector('#context-aware').checked = localStorage.getItem('context-aware') === 'true';
    }

    hideSettings() {
        this.container.querySelector('#ai-settings-modal').classList.add('hidden');
    }

    saveSettings() {
        this.apiKey = this.container.querySelector('#gemini-api-key').value;
        const model = this.container.querySelector('#ai-model').value;
        const autoExecute = this.container.querySelector('#agentic-auto-execute').checked;
        const contextAware = this.container.querySelector('#context-aware').checked;
        
        localStorage.setItem('gemini-api-key', this.apiKey);
        localStorage.setItem('ai-model', model);
        localStorage.setItem('agentic-auto-execute', autoExecute);
        localStorage.setItem('context-aware', contextAware);
        
        this.hideSettings();
        this.updateStatus('Settings saved', 'success');
        
        setTimeout(() => {
            this.updateStatus('Ready', 'ready');
        }, 2000);
    }

    loadSettings() {
        this.apiKey = localStorage.getItem('gemini-api-key') || '';
    }

    clearChat() {
        this.container.querySelector('#ai-messages').innerHTML = '';
        this.conversationHistory = [];
    }

    attachCurrentFile() {
        const context = this.getCurrentFileContext();
        if (!context) {
            this.showError('No file is currently open in the editor.');
            return;
        }
        
        const input = this.container.querySelector('#ai-input');
        const currentValue = input.value;
        const attachment = `\n\n[Attached: ${context.name}]\n`;
        input.value = currentValue + attachment;
        input.focus();
    }

    updateStatus(text, type = 'ready') {
        const statusElement = this.container.querySelector('#ai-status .status-text');
        const indicator = this.container.querySelector('#ai-status .status-indicator');
        
        statusElement.textContent = text;
        indicator.className = `status-indicator ${type}`;
    }

    showError(message) {
        this.updateStatus(message, 'error');
        setTimeout(() => {
            this.updateStatus('Ready', 'ready');
        }, 5000);
    }
}
