// Test script to verify file explorer arrow error fix
// Run this in the browser console to test the fix

function testFileExplorerFix() {
    console.log('🧪 Testing File Explorer arrow error fix...');
    
    // Test 1: Check if FileExplorer is available
    console.log('\n1. Testing FileExplorer availability...');
    if (window.rustCodeApp && window.rustCodeApp.fileExplorer) {
        console.log('✅ FileExplorer is available');
    } else {
        console.log('❌ FileExplorer not found');
        return;
    }
    
    // Test 2: Check if file explorer container exists
    console.log('\n2. Testing file explorer container...');
    const fileExplorerContainer = document.getElementById('file-explorer');
    if (fileExplorerContainer) {
        console.log('✅ File explorer container found');
    } else {
        console.log('❌ File explorer container not found');
        return;
    }
    
    // Test 3: Check for existing directory items
    console.log('\n3. Testing existing directory items...');
    const directoryItems = fileExplorerContainer.querySelectorAll('.file-tree-item[data-is-directory="true"]');
    console.log(`📁 Found ${directoryItems.length} directory items`);
    
    if (directoryItems.length > 0) {
        // Test 4: Test clicking on a directory
        console.log('\n4. Testing directory click functionality...');
        const firstDirectory = directoryItems[0];
        const directoryName = firstDirectory.querySelector('.file-tree-label')?.textContent || 'Unknown';
        
        console.log(`🖱️ Testing click on directory: ${directoryName}`);
        
        // Check if arrow exists
        const arrow = firstDirectory.querySelector('.file-tree-arrow');
        if (arrow) {
            console.log('✅ Arrow element found for directory');
        } else {
            console.log('⚠️ No arrow element found for directory');
        }
        
        // Simulate click
        try {
            firstDirectory.click();
            console.log('✅ Directory click successful - no errors thrown');
        } catch (error) {
            console.log('❌ Directory click failed:', error.message);
        }
    } else {
        console.log('⚠️ No directories found to test');
        
        // Test 5: Create a test directory structure
        console.log('\n5. Creating test directory structure...');
        testCreateDirectory();
    }
    
    // Test 6: Test the toggleFolder method directly
    console.log('\n6. Testing toggleFolder method...');
    const fileExplorer = window.rustCodeApp.fileExplorer;
    if (fileExplorer && typeof fileExplorer.toggleFolder === 'function') {
        console.log('✅ toggleFolder method is available');
        
        // Test with mock data
        const mockNode = {
            path: '/test',
            name: 'test',
            is_directory: true
        };
        
        const mockLi = document.createElement('li');
        mockLi.className = 'file-tree-item';
        mockLi.innerHTML = '<span class="file-tree-arrow"></span><span class="file-tree-icon"></span>';
        
        try {
            // This should not throw an error now
            fileExplorer.toggleFolder(mockNode, mockLi, null);
            console.log('✅ toggleFolder method works with null arrow parameter');
        } catch (error) {
            console.log('❌ toggleFolder method failed:', error.message);
        }
    } else {
        console.log('❌ toggleFolder method not available');
    }
    
    console.log('\n🎉 File Explorer arrow error fix testing completed!');
}

async function testCreateDirectory() {
    try {
        console.log('📁 Creating test directory...');
        
        if (window.rustCodeApp && window.rustCodeApp.invoke) {
            await window.rustCodeApp.invoke('create_directory', { path: '/test-folder' });
            console.log('✅ Test directory created');
            
            // Refresh file explorer
            if (window.rustCodeApp.fileExplorer && window.rustCodeApp.fileExplorer.refresh) {
                window.rustCodeApp.fileExplorer.refresh();
                console.log('✅ File explorer refreshed');
            }
        } else {
            console.log('⚠️ Cannot create test directory - invoke method not available');
        }
    } catch (error) {
        console.log('❌ Failed to create test directory:', error.message);
    }
}

// Test error console integration
function testErrorConsoleIntegration() {
    console.log('\n🔍 Testing error console integration...');
    
    if (window.errorConsole) {
        console.log('✅ Error console is available');
        
        // Log a test message
        window.errorConsole.logInfo('File Explorer fix test completed successfully', 'test-file-explorer-fix.js', 1);
        console.log('✅ Test message logged to error console');
    } else {
        console.log('❌ Error console not available');
    }
}

// Auto-run the test if this script is loaded
if (typeof window !== 'undefined') {
    // Wait for the app to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testFileExplorerFix();
                testErrorConsoleIntegration();
            }, 2000);
        });
    } else {
        setTimeout(() => {
            testFileExplorerFix();
            testErrorConsoleIntegration();
        }, 2000);
    }
}

// Make the test function globally available
if (typeof window !== 'undefined') {
    window.testFileExplorerFix = testFileExplorerFix;
    window.testCreateDirectory = testCreateDirectory;
}

console.log('🔧 File Explorer fix test script loaded. Run testFileExplorerFix() to test.');
